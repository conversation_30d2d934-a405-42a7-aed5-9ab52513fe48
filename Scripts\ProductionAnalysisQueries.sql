-- CaliberLIMS Production Analysis Queries
-- Use these queries to analyze production issues from NLog database

-- 1. API Performance Analysis - Last 24 Hours
SELECT 
    JSON_VALUE(properties, '$.Controller') as Controller,
    JSON_VALUE(properties, '$.Action') as Action,
    COUNT(*) as RequestCount,
    AVG(CAST(JSON_VALUE(properties, '$.ResponseTimeMs') AS FLOAT)) as AvgResponseTime,
    MIN(CAST(JSON_VALUE(properties, '$.ResponseTimeMs') AS FLOAT)) as MinResponseTime,
    MAX(CAST(JSON_VALUE(properties, '$.ResponseTimeMs') AS FLOAT)) as MaxResponseTime,
    PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY CAST(JSON_VALUE(properties, '$.ResponseTimeMs') AS FLOAT)) as P95ResponseTime
FROM NLogTable 
WHERE Datetime >= DATEADD(HOUR, -24, GETDATE())
    AND Message LIKE 'API_RESPONSE%'
    AND JSON_VALUE(properties, '$.ResponseTimeMs') IS NOT NULL
GROUP BY JSON_VALUE(properties, '$.Controller'), JSON_VALUE(properties, '$.Action')
ORDER BY AvgResponseTime DESC;

-- 2. Error Analysis by Module
SELECT 
    JSON_VALUE(properties, '$.Controller') as Module,
    COUNT(*) as ErrorCount,
    COUNT(DISTINCT JSON_VALUE(properties, '$.UserId')) as AffectedUsers,
    COUNT(DISTINCT JSON_VALUE(properties, '$.PlantId')) as AffectedPlants,
    MAX(Datetime) as LastOccurrence,
    STRING_AGG(DISTINCT SUBSTRING(Message, 1, 100), '; ') as ErrorSamples
FROM NLogTable 
WHERE Datetime >= DATEADD(HOUR, -24, GETDATE())
    AND Level = 'Error'
    AND JSON_VALUE(properties, '$.Controller') IS NOT NULL
GROUP BY JSON_VALUE(properties, '$.Controller')
ORDER BY ErrorCount DESC;

-- 3. Database Performance Analysis
SELECT 
    JSON_VALUE(properties, '$.StoredProcedure') as StoredProcedure,
    COUNT(*) as ExecutionCount,
    AVG(CAST(JSON_VALUE(properties, '$.ExecutionTimeMs') AS FLOAT)) as AvgExecutionTime,
    MAX(CAST(JSON_VALUE(properties, '$.ExecutionTimeMs') AS FLOAT)) as MaxExecutionTime,
    COUNT(CASE WHEN CAST(JSON_VALUE(properties, '$.ExecutionTimeMs') AS FLOAT) > 10000 THEN 1 END) as SlowQueries,
    COUNT(CASE WHEN Level = 'Error' THEN 1 END) as ErrorCount
FROM NLogTable 
WHERE Datetime >= DATEADD(HOUR, -24, GETDATE())
    AND Message LIKE 'DB_OPERATION%'
    AND JSON_VALUE(properties, '$.StoredProcedure') IS NOT NULL
GROUP BY JSON_VALUE(properties, '$.StoredProcedure')
ORDER BY AvgExecutionTime DESC;

-- 4. User Activity Analysis
SELECT 
    JSON_VALUE(properties, '$.UserId') as UserId,
    JSON_VALUE(properties, '$.PlantId') as PlantId,
    COUNT(*) as RequestCount,
    COUNT(CASE WHEN Level = 'Error' THEN 1 END) as ErrorCount,
    AVG(CAST(JSON_VALUE(properties, '$.ResponseTimeMs') AS FLOAT)) as AvgResponseTime,
    MIN(Datetime) as FirstActivity,
    MAX(Datetime) as LastActivity
FROM NLogTable 
WHERE Datetime >= DATEADD(HOUR, -24, GETDATE())
    AND Message LIKE 'API_%'
    AND JSON_VALUE(properties, '$.UserId') IS NOT NULL
    AND JSON_VALUE(properties, '$.UserId') != 'Unknown'
GROUP BY JSON_VALUE(properties, '$.UserId'), JSON_VALUE(properties, '$.PlantId')
HAVING COUNT(CASE WHEN Level = 'Error' THEN 1 END) > 0
ORDER BY ErrorCount DESC;

-- 5. Shared Services Usage Pattern
SELECT 
    DATEPART(HOUR, Datetime) as Hour,
    JSON_VALUE(properties, '$.Controller') as Service,
    COUNT(*) as RequestCount,
    AVG(CAST(JSON_VALUE(properties, '$.ResponseTimeMs') AS FLOAT)) as AvgResponseTime,
    COUNT(CASE WHEN Level = 'Error' THEN 1 END) as ErrorCount
FROM NLogTable 
WHERE Datetime >= DATEADD(HOUR, -24, GETDATE())
    AND Message LIKE 'API_%'
    AND JSON_VALUE(properties, '$.Controller') IN (
        'CommonController', 'MastersController', 'SampleManagerController',
        'ReserveSampleController', 'ChemicalController', 'InstrumentController',
        'LDMSController', 'StabilityController'
    )
GROUP BY DATEPART(HOUR, Datetime), JSON_VALUE(properties, '$.Controller')
ORDER BY Hour, Service;

-- 6. Integration Issues Analysis
SELECT 
    JSON_VALUE(properties, '$.SystemName') as ExternalSystem,
    JSON_VALUE(properties, '$.Operation') as Operation,
    COUNT(*) as FailureCount,
    MAX(Datetime) as LastFailure,
    STRING_AGG(DISTINCT SUBSTRING(JSON_VALUE(properties, '$.Exception'), 1, 200), '; ') as ErrorSamples
FROM NLogTable 
WHERE Datetime >= DATEADD(HOUR, -24, GETDATE())
    AND Message LIKE 'INTEGRATION_ERROR%'
GROUP BY JSON_VALUE(properties, '$.SystemName'), JSON_VALUE(properties, '$.Operation')
ORDER BY FailureCount DESC;

-- 7. Validation Errors by Object Type
SELECT 
    JSON_VALUE(properties, '$.ObjectType') as ObjectType,
    JSON_VALUE(properties, '$.ValidationRule') as ValidationRule,
    COUNT(*) as ValidationFailures,
    COUNT(DISTINCT JSON_VALUE(properties, '$.UserId')) as AffectedUsers,
    MAX(Datetime) as LastOccurrence
FROM NLogTable 
WHERE Datetime >= DATEADD(HOUR, -24, GETDATE())
    AND Message LIKE 'VALIDATION_ERROR%'
GROUP BY JSON_VALUE(properties, '$.ObjectType'), JSON_VALUE(properties, '$.ValidationRule')
ORDER BY ValidationFailures DESC;

-- 8. System Resource Trends
SELECT 
    DATEPART(HOUR, Datetime) as Hour,
    AVG(CAST(JSON_VALUE(properties, '$.WorkingSet') AS FLOAT)) / (1024*1024) as AvgMemoryMB,
    AVG(CAST(JSON_VALUE(properties, '$.ThreadCount') AS FLOAT)) as AvgThreadCount,
    AVG(CAST(JSON_VALUE(properties, '$.CpuTime') AS FLOAT)) as AvgCpuTime
FROM NLogTable 
WHERE Datetime >= DATEADD(HOUR, -24, GETDATE())
    AND Message LIKE '%SystemState%'
    AND JSON_VALUE(properties, '$.WorkingSet') IS NOT NULL
GROUP BY DATEPART(HOUR, Datetime)
ORDER BY Hour;

-- 9. Critical Path Analysis (Trace specific request)
-- Replace 'YOUR_CORRELATION_ID' with actual correlation ID
SELECT 
    Datetime,
    Level,
    Message,
    JSON_VALUE(properties, '$.Controller') as Controller,
    JSON_VALUE(properties, '$.Action') as Action,
    JSON_VALUE(properties, '$.ResponseTimeMs') as ResponseTime,
    JSON_VALUE(properties, '$.StoredProcedure') as StoredProcedure,
    JSON_VALUE(properties, '$.ExecutionTimeMs') as DbExecutionTime
FROM NLogTable 
WHERE JSON_VALUE(properties, '$.TraceId') = 'YOUR_CORRELATION_ID'
ORDER BY Datetime;

-- 10. Plant-wise Performance Comparison
SELECT 
    JSON_VALUE(properties, '$.PlantId') as PlantId,
    COUNT(*) as TotalRequests,
    COUNT(CASE WHEN Level = 'Error' THEN 1 END) as ErrorCount,
    CAST(COUNT(CASE WHEN Level = 'Error' THEN 1 END) AS FLOAT) / COUNT(*) * 100 as ErrorRate,
    AVG(CAST(JSON_VALUE(properties, '$.ResponseTimeMs') AS FLOAT)) as AvgResponseTime
FROM NLogTable 
WHERE Datetime >= DATEADD(HOUR, -24, GETDATE())
    AND Message LIKE 'API_%'
    AND JSON_VALUE(properties, '$.PlantId') IS NOT NULL
    AND JSON_VALUE(properties, '$.PlantId') != 'Unknown'
GROUP BY JSON_VALUE(properties, '$.PlantId')
ORDER BY ErrorRate DESC;

-- 11. Peak Usage Analysis
SELECT 
    DATEPART(HOUR, Datetime) as Hour,
    COUNT(*) as RequestCount,
    COUNT(CASE WHEN Level = 'Error' THEN 1 END) as ErrorCount,
    AVG(CAST(JSON_VALUE(properties, '$.ResponseTimeMs') AS FLOAT)) as AvgResponseTime,
    COUNT(DISTINCT JSON_VALUE(properties, '$.UserId')) as ActiveUsers
FROM NLogTable 
WHERE Datetime >= DATEADD(DAY, -7, GETDATE())
    AND Message LIKE 'API_%'
GROUP BY DATEPART(HOUR, Datetime)
ORDER BY RequestCount DESC;

-- 12. Slow Query Identification
SELECT TOP 20
    JSON_VALUE(properties, '$.StoredProcedure') as StoredProcedure,
    CAST(JSON_VALUE(properties, '$.ExecutionTimeMs') AS FLOAT) as ExecutionTime,
    JSON_VALUE(properties, '$.Parameters') as Parameters,
    JSON_VALUE(properties, '$.UserId') as UserId,
    JSON_VALUE(properties, '$.PlantId') as PlantId,
    Datetime
FROM NLogTable 
WHERE Datetime >= DATEADD(HOUR, -24, GETDATE())
    AND Message LIKE 'DB_OPERATION%'
    AND CAST(JSON_VALUE(properties, '$.ExecutionTimeMs') AS FLOAT) > 10000
ORDER BY CAST(JSON_VALUE(properties, '$.ExecutionTimeMs') AS FLOAT) DESC;
