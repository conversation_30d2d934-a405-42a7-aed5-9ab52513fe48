# CaliberLIMS Production Monitoring Script
# Monitors shared services APIs and generates alerts

param(
    [string]$ApiBaseUrl = "https://your-caliberlims-api",
    [string]$AlertEmail = "<EMAIL>",
    [int]$CheckIntervalMinutes = 5
)

# Configuration
$ErrorThreshold = 10  # Errors per hour
$ResponseTimeThreshold = 5000  # Milliseconds
$MemoryThreshold = 2048  # MB

function Send-Alert {
    param(
        [string]$Subject,
        [string]$Body,
        [string]$Severity = "Warning"
    )
    
    Write-Host "[$Severity] $Subject" -ForegroundColor $(if($Severity -eq "Critical") {"Red"} else {"Yellow"})
    Write-Host $Body
    
    # Send email alert (implement based on your email system)
    # Send-MailMessage -To $AlertEmail -Subject "CaliberLIMS Alert: $Subject" -Body $Body
}

function Test-ApiHealth {
    try {
        $response = Invoke-RestMethod -Uri "$ApiBaseUrl/api/ProductionDashboard/health" -Method Get -TimeoutSec 30
        return $response
    }
    catch {
        Send-Alert -Subject "API Health Check Failed" -Body "Failed to connect to CaliberLIMS API: $($_.Exception.Message)" -Severity "Critical"
        return $null
    }
}

function Test-ApiPerformance {
    try {
        $response = Invoke-RestMethod -Uri "$ApiBaseUrl/api/ProductionDashboard/performance?hours=1" -Method Get
        
        foreach ($metric in $response.Metrics) {
            if ($metric.AvgResponseTime -gt $ResponseTimeThreshold) {
                Send-Alert -Subject "Slow API Performance Detected" -Body "Average response time: $($metric.AvgResponseTime)ms (Threshold: $ResponseTimeThreshold ms)"
            }
            
            if ($metric.ErrorCount -gt $ErrorThreshold) {
                Send-Alert -Subject "High Error Rate Detected" -Body "Error count: $($metric.ErrorCount) in the last hour (Threshold: $ErrorThreshold)"
            }
        }
        
        return $response
    }
    catch {
        Send-Alert -Subject "Performance Check Failed" -Body "Failed to get performance metrics: $($_.Exception.Message)"
        return $null
    }
}

function Test-SharedServices {
    try {
        $response = Invoke-RestMethod -Uri "$ApiBaseUrl/api/ProductionDashboard/services-usage?hours=1" -Method Get
        
        foreach ($service in $response.Services.PSObject.Properties) {
            $serviceName = $service.Name
            $serviceData = $service.Value
            
            if ($serviceData.ErrorCount -gt 5) {
                Send-Alert -Subject "Service Error Alert: $serviceName" -Body "Service $serviceName has $($serviceData.ErrorCount) errors in the last hour"
            }
            
            if ($serviceData.AvgResponseTime -gt $ResponseTimeThreshold) {
                Send-Alert -Subject "Service Performance Alert: $serviceName" -Body "Service $serviceName average response time: $($serviceData.AvgResponseTime)ms"
            }
        }
        
        return $response
    }
    catch {
        Send-Alert -Subject "Shared Services Check Failed" -Body "Failed to get shared services status: $($_.Exception.Message)"
        return $null
    }
}

function Test-DatabasePerformance {
    try {
        $response = Invoke-RestMethod -Uri "$ApiBaseUrl/api/ProductionDashboard/database-performance?hours=1" -Method Get
        
        foreach ($metric in $response.DatabaseMetrics) {
            if ($metric.AvgExecutionTime -gt 10000) {  # 10 seconds
                Send-Alert -Subject "Slow Database Query Detected" -Body "Stored Procedure: $($metric.StoredProcedure), Avg Execution Time: $($metric.AvgExecutionTime)ms"
            }
            
            if ($metric.ErrorCount -gt 0) {
                Send-Alert -Subject "Database Error Detected" -Body "Stored Procedure: $($metric.StoredProcedure), Error Count: $($metric.ErrorCount)"
            }
        }
        
        return $response
    }
    catch {
        Send-Alert -Subject "Database Performance Check Failed" -Body "Failed to get database performance metrics: $($_.Exception.Message)"
        return $null
    }
}

function Test-SystemAlerts {
    try {
        $response = Invoke-RestMethod -Uri "$ApiBaseUrl/api/ProductionDashboard/alerts" -Method Get
        
        foreach ($alert in $response.Alerts) {
            Send-Alert -Subject "System Alert: $($alert.Type)" -Body $alert.Message -Severity $alert.Severity
        }
        
        # Check memory usage
        if ($response.SystemState.WorkingSet -gt ($MemoryThreshold * 1024 * 1024)) {
            $memoryMB = [math]::Round($response.SystemState.WorkingSet / (1024 * 1024), 2)
            Send-Alert -Subject "High Memory Usage" -Body "Current memory usage: $memoryMB MB (Threshold: $MemoryThreshold MB)"
        }
        
        return $response
    }
    catch {
        Send-Alert -Subject "System Alerts Check Failed" -Body "Failed to get system alerts: $($_.Exception.Message)"
        return $null
    }
}

function Generate-HealthReport {
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Write-Host "=== CaliberLIMS Health Report - $timestamp ===" -ForegroundColor Green
    
    # Test API Health
    Write-Host "Checking API Health..." -ForegroundColor Cyan
    $health = Test-ApiHealth
    if ($health) {
        Write-Host "✓ API Health: $($health.Status)" -ForegroundColor Green
    }
    
    # Test Performance
    Write-Host "Checking API Performance..." -ForegroundColor Cyan
    $performance = Test-ApiPerformance
    if ($performance) {
        Write-Host "✓ Performance metrics collected" -ForegroundColor Green
    }
    
    # Test Shared Services
    Write-Host "Checking Shared Services..." -ForegroundColor Cyan
    $services = Test-SharedServices
    if ($services) {
        Write-Host "✓ Shared services status checked" -ForegroundColor Green
    }
    
    # Test Database Performance
    Write-Host "Checking Database Performance..." -ForegroundColor Cyan
    $database = Test-DatabasePerformance
    if ($database) {
        Write-Host "✓ Database performance metrics collected" -ForegroundColor Green
    }
    
    # Test System Alerts
    Write-Host "Checking System Alerts..." -ForegroundColor Cyan
    $alerts = Test-SystemAlerts
    if ($alerts) {
        Write-Host "✓ System alerts checked" -ForegroundColor Green
    }
    
    Write-Host "=== Health Report Complete ===" -ForegroundColor Green
    Write-Host ""
}

# Main execution
Write-Host "Starting CaliberLIMS Production Monitoring..." -ForegroundColor Yellow
Write-Host "API Base URL: $ApiBaseUrl" -ForegroundColor Yellow
Write-Host "Check Interval: $CheckIntervalMinutes minutes" -ForegroundColor Yellow
Write-Host ""

# Continuous monitoring loop
while ($true) {
    try {
        Generate-HealthReport
        
        # Wait for next check
        Start-Sleep -Seconds ($CheckIntervalMinutes * 60)
    }
    catch {
        Write-Host "Monitoring error: $($_.Exception.Message)" -ForegroundColor Red
        Start-Sleep -Seconds 60  # Wait 1 minute before retrying
    }
}
